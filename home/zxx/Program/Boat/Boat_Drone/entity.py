import numpy as np
import math
from typing import List
import random

# 实体类
class Entity:
    def __init__(self, pos: np.n<PERSON>ray, vel: float, heading: float, min_turn_radius: float, max_speed: float, min_speed: float = 0):
        self.pos = pos.astype(float)
        self.vel = vel
        self.heading = heading  # 弧度
        self.min_turn_radius = min_turn_radius
        self.max_speed = max_speed
        self.min_speed = min_speed

    def update_position(self, dt: float, target_heading: float, target_speed: float):
        # 简单运动学模型，考虑最小转弯半径（使用Dubins-like更新）
        angular_vel = (target_heading - self.heading) / dt if abs(target_heading - self.heading) < abs(2*math.pi+target_heading-self.heading) else (2*math.pi+target_heading-self.heading) / dt
        max_angular_vel = self.vel / self.min_turn_radius if self.vel > 0 else 0
        angular_vel = np.clip(angular_vel, -max_angular_vel, max_angular_vel)
        self.heading += angular_vel * dt
        self.vel = np.clip(target_speed, self.min_speed, self.max_speed)
        self.pos += np.array([self.vel * math.cos(self.heading), self.vel * math.sin(self.heading)]) * dt

class Boat(Entity):
    def __init__(self, pos, vel=0, heading=0):
        super().__init__(pos, vel, heading, min_turn_radius=0.02, max_speed=0.01, min_speed=0)
        self.detect_range = 20
        self.side = 'white'
        self.lock_range = 40    
        self.lock_target = None
        self.lock_time = 0
        self.lock_success = 0
        self.frozen = False
        self.lock_count = 0
        self.frozen_time = 0
        self.exited = False
        self.active = True

    # def detect(self, targets):
    #     # 圆形探测
    #     detected = [t for t in targets if np.linalg.norm(self.pos - t.pos) < self.detect_range]
    #     return detected
    def detect(self, targets):
        detected = []
        for target in targets:
            if target.exited:
                continue
            if np.linalg.norm(self.pos - target.pos) < self.detect_range:
                detected.append(target)
        return detected

    def be_locked(self):
        self.lock_count += 1
        if self.lock_count == 1:
            self.frozen_time = 300
        elif self.lock_count >= 2:
            self.exited = True

    def lock(self, target: 'Boat', dt):
        if self.frozen_time > 0 or self.lock_count >= 2:
            return False
        if self.lock_target != target:
            self.lock_time = 0
            self.lock_target = target
        if np.linalg.norm(self.pos - target.pos) > self.lock_range:
            self.lock_target = None
            self.lock_time = 0
            return False
        self.lock_time += dt
        if self.lock_time > 300:  # 5分钟=300秒
            if random.random() < 0.8:
                target.be_locked()
                self.lock_success += 1 if hasattr(self, 'side') and self.side == 'white' else 0
                self.lock_target = None
                self.lock_time = 0
                return True
            else:
                self.lock_time = 0
                
        return False

class Drone(Entity):
    def __init__(self, pos, vel=0.02, heading=0):
        super().__init__(pos, vel, heading, min_turn_radius=0.1, max_speed=0.15, min_speed=0.02)
        self.frozen_time = 0
        self.detect_angle = math.radians(60)  # 60度扇形
        self.detect_range = 60  # 假设
        self.energy = 1.0  # 满能量
        self.max_air_time = 7200
        self.charge_time = 18000
        self.charging = False
        self.on_boat = None
        self.active = True
        
        # 跟踪状态管理
        self.tracking_target = None
        self.tracking_mode = False
        self.ideal_tracking_distance = 35.0  # 理想跟踪距离（千米，实际35米）
        self.min_tracking_distance = 25.0    # 最小跟踪距离
        self.max_tracking_distance = 45.0    # 最大跟踪距离
        self.tracking_lost_time = 0          # 失去目标的时间
        self.max_tracking_lost_time = 500    # 最大失去目标时间（5秒）

    def start_tracking(self, target):
        """开始跟踪目标"""
        self.tracking_target = target
        self.tracking_mode = True
        self.tracking_lost_time = 0

    def stop_tracking(self):
        """停止跟踪"""
        self.tracking_target = None
        self.tracking_mode = False
        self.tracking_lost_time = 0

    def update_tracking(self, dt, detected_targets):
        """更新跟踪状态和行为"""
        if not self.tracking_mode or not self.tracking_target:
            return None, None
            
        # 检查目标是否仍在探测范围内
        target_in_range = self.tracking_target in detected_targets
        
        if target_in_range:
            self.tracking_lost_time = 0
            # 计算到目标的距离和方向
            target_vec = self.tracking_target.pos - self.pos
            distance = np.linalg.norm(target_vec)
            
            if distance > 0:
                # 计算目标朝向的单位向量
                target_direction = np.array([math.cos(self.tracking_target.heading), 
                                           math.sin(self.tracking_target.heading)])
                
                # 计算理想跟踪位置（目标后方理想距离处）
                ideal_pos = self.tracking_target.pos - target_direction * self.ideal_tracking_distance
                
                # 计算当前位置到理想位置的向量
                to_ideal_vec = ideal_pos - self.pos
                to_ideal_distance = np.linalg.norm(to_ideal_vec)
                
                # 距离误差（正值表示需要靠近，负值表示需要远离）
                distance_error = distance - self.ideal_tracking_distance
                
                # 计算朝向：指向理想位置
                if to_ideal_distance > 0.1:  # 避免除零
                    target_heading = math.atan2(to_ideal_vec[1], to_ideal_vec[0])
                else:
                    # 如果已经很接近理想位置，保持当前朝向
                    target_heading = self.heading
                
                # 速度控制：基于距离误差的比例控制
                base_speed = self.tracking_target.vel  # 基础速度匹配目标
                
                # 距离控制增益
                kp_distance = 0.002  # 比例控制增益
                speed_adjustment = kp_distance * distance_error
                
                # 计算目标速度
                target_speed = base_speed + speed_adjustment
                
                # 额外的距离分段控制
                if distance < self.min_tracking_distance:
                    # 太近了，强制减速并远离
                    print('near')
                    target_speed = min(target_speed, 0)
                    # 计算远离方向
                    away_vec = self.pos - self.tracking_target.pos
                    if np.linalg.norm(away_vec) > 0:
                        away_heading = math.atan2(away_vec[1], away_vec[0])
                        # 混合远离方向和理想位置方向
                        target_heading = 0.7 * away_heading + 0.3 * target_heading
                        
                elif distance > self.max_tracking_distance:
                    # 太远了，加速追赶
                    target_speed = max(target_speed, self.max_speed * 0.9)
                
                # 限制速度范围
                target_speed = np.clip(target_speed, self.min_speed, self.max_speed)
                
                return target_heading, target_speed
        else:
            # 目标不在探测范围内
            self.tracking_lost_time += dt
            if self.tracking_lost_time > self.max_tracking_lost_time:
                self.stop_tracking()
                return None, None
            
            # 继续朝最后已知方向搜索
            if hasattr(self.tracking_target, 'pos'):
                search_vec = self.tracking_target.pos - self.pos
                target_heading = math.atan2(search_vec[1], search_vec[0])
                return target_heading, self.max_speed * 0.8
        
        return None, None

    def detect(self, targets: List['Boat']) -> List['Boat']:
        # 前方60度扇形探测
        detected = []
        for t in targets:
            vec = t.pos - self.pos
            dist = np.linalg.norm(vec)
            if dist > self.detect_range:
                continue
            angle = math.atan2(vec[1], vec[0]) - self.heading
            angle = (angle + math.pi) % (2 * math.pi) - math.pi
            if abs(angle) < self.detect_angle / 2:
                detected.append(t)
        return detected

    def update_energy(self, dt):
        if self.charging and self.on_boat.frozen_time > 0:
            return  # 暂停补能
        if self.charging:
            self.energy = min(1.0, self.energy + dt/self.charge_time)
        else:
            self.energy = max(0, self.energy - dt/self.max_air_time)
            # self.energy = 1
