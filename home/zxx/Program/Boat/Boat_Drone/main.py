import numpy as np

from env import UnmannedClusterEnv
import pygame
import time
import math

def main():
    env = UnmannedClusterEnv()
    env.auto_coordination = True  # 启用自动协调
    obs = env.reset()
    
    # 初始化渲染
    env.render()
    
    done = False
    step_count = 0
    i = 0
    while not done:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                env.close()
                return
        # 生成随机动作 (-1到1之间的随机值)
        # action = np.random.uniform(-1, 1, size=env.action_space.shape)
        # print(len(env.white_boats))
        action = np.array([1, 0.5] * (env.action_space.shape[0]//2)) if step_count < 10 else np.array([1, -0.4] * (env.action_space.shape[0]//2))
        
        # 执行一步
        # print(obs)
        black_1_pos = [obs[12], obs[13]]
        black_2_pos = [obs[18], obs[19]]
        white_boat_pos = [obs[0], obs[1]]
        white_drone_pos = [obs[6], obs[7]]
        # mid_pos = [(black_1_pos[0] + black_2_pos[0]) / 2, (black_1_pos[1] + black_2_pos[1]) / 2]
        # heading = math.atan2(mid_pos[1] - white_boat_pos[1], mid_pos[0] - white_boat_pos[0])
        heading = math.atan2(black_1_pos[1] - white_boat_pos[1], black_1_pos[0] - white_boat_pos[0])
        heading_drone = math.atan2(black_1_pos[1] - white_drone_pos[1], black_1_pos[0] - white_drone_pos[0])
        # print(heading)
        # d_head = heading - obs[3]
        # normalized_d_head = d_head / math.pi
        normalized_d_head = heading / math.pi
        action[1] = normalized_d_head
        action[3] = heading_drone / math.pi
        # print(black_1_pos[1], black_1_pos[0], white_drone_pos[1], white_drone_pos[0])
        obs, reward, done, info = env.step(action)
        # 渲染
        env.render()
        
        # 打印一些信息
        # print(f"Step {step_count}:")
        # print(f"  Reward: {reward}")
        # print(f"  Intercepted: {env.intercepted}")
        # print(f"  Be Locked: {env.be_locked}")
        # print(f"  Collisions: {env.collisions}")
        # print(f"  Black Boats remaining: {len([b for b in env.black_boats if b.active])}")
        # print(f"lock_success: {env.lock_success}")
        # print(f"total_lock_success: {env.total_lock_success}")
        # print(f"total_black_intercepted: {env.total_black_intercepted}")
        # print(f"total_be_locked: {env.total_be_locked}")
        # print(f"total_collisions: {env.total_collisions}")
        
        # 监控无人机巡视状态
        if hasattr(env, 'coordination_controller') and env.coordination_controller:
            controller = env.coordination_controller
            for i, drone in enumerate(env.white_drones):
                if drone.active and not drone.on_boat:
                    drone_id = id(drone)
                    state = controller.drone_states.get(drone_id, {})
                    if state.get('circling', False):
                        progress = (state.get('circle_angle', 0) / (2 * np.pi)) * 100
                        print(f"Drone {i}: ★ CIRCLING ({progress:.0f}%)")
        
        step_count += 1
        time.sleep(0.03)  # 减慢速度以便观察
        
        # 检查是否结束
        if done:
            print("Episode finished!")
            print(f"Total steps: {step_count}")
            print(f"Final reward: {reward}")
            break
    
    # 保持窗口打开直到用户关闭
    while True:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                env.close()
                return
        env.render()

if __name__ == "__main__":
    main()
